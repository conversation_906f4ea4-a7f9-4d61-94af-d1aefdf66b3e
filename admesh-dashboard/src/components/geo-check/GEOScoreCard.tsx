import { Card, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";

interface GEOScoreCardProps {
  title: string;
  score: number;
  maxScore?: number;
  description: string;
  trend?: "up" | "down" | "stable";
}

export function GEOScoreCard({ 
  title, 
  score, 
  maxScore = 100, 
  description, 
  trend 
}: GEOScoreCardProps) {
  const getScoreColor = (score: number) => {
    if (score >= 80) return "text-green-600";
    if (score >= 60) return "text-yellow-600";
    return "text-red-600";
  };

  const getScoreBadgeVariant = (score: number) => {
    if (score >= 80) return "default";
    if (score >= 60) return "secondary";
    return "destructive";
  };

  const getScoreLabel = (score: number) => {
    if (score >= 80) return "Excellent";
    if (score >= 60) return "Good";
    return "Needs Improvement";
  };

  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <div className="text-2xl font-bold">
              <span className={getScoreColor(score)}>{score}</span>
              <span className="text-muted-foreground text-sm">/{maxScore}</span>
            </div>
            <Badge variant={getScoreBadgeVariant(score)}>
              {getScoreLabel(score)}
            </Badge>
          </div>
          
          <Progress value={(score / maxScore) * 100} className="h-2" />
          
          <p className="text-xs text-muted-foreground">{description}</p>
          
          {trend && (
            <div className="flex items-center gap-1 text-xs">
              {trend === "up" && (
                <>
                  <span className="text-green-600">↗</span>
                  <span className="text-green-600">Improving</span>
                </>
              )}
              {trend === "down" && (
                <>
                  <span className="text-red-600">↘</span>
                  <span className="text-red-600">Declining</span>
                </>
              )}
              {trend === "stable" && (
                <>
                  <span className="text-gray-600">→</span>
                  <span className="text-gray-600">Stable</span>
                </>
              )}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
