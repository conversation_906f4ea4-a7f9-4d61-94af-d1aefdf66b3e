import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { 
  AlertTriangle, 
  CheckCircle, 
  Info, 
  ExternalLink,
  BookOpen 
} from "lucide-react";

interface GEORecommendation {
  priority: "high" | "medium" | "low";
  category: string;
  title: string;
  description: string;
  impact: string;
}

interface GEORecommendationCardProps {
  recommendation: GEORecommendation;
  onLearnMore?: () => void;
}

export function GEORecommendationCard({ 
  recommendation, 
  onLearnMore 
}: GEORecommendationCardProps) {
  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case "high":
        return <AlertTriangle className="h-4 w-4 text-red-600" />;
      case "medium":
        return <Info className="h-4 w-4 text-yellow-600" />;
      case "low":
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      default:
        return <Info className="h-4 w-4 text-gray-600" />;
    }
  };

  const getPriorityBadgeVariant = (priority: string) => {
    switch (priority) {
      case "high":
        return "destructive";
      case "medium":
        return "secondary";
      case "low":
        return "outline";
      default:
        return "outline";
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high":
        return "border-l-red-500";
      case "medium":
        return "border-l-yellow-500";
      case "low":
        return "border-l-green-500";
      default:
        return "border-l-gray-500";
    }
  };

  return (
    <Card className={`border-l-4 ${getPriorityColor(recommendation.priority)}`}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-2">
            {getPriorityIcon(recommendation.priority)}
            <CardTitle className="text-base">{recommendation.title}</CardTitle>
          </div>
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="text-xs">
              {recommendation.category}
            </Badge>
            <Badge variant={getPriorityBadgeVariant(recommendation.priority)} className="text-xs">
              {recommendation.priority.toUpperCase()}
            </Badge>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <p className="text-sm text-muted-foreground">
          {recommendation.description}
        </p>
        
        <div className="bg-muted/50 p-3 rounded-lg">
          <div className="flex items-center gap-2 mb-1">
            <span className="text-xs font-medium text-muted-foreground">Expected Impact:</span>
          </div>
          <p className="text-sm">{recommendation.impact}</p>
        </div>

        <div className="flex items-center gap-2 pt-2">
          <Button 
            variant="outline" 
            size="sm" 
            className="gap-2"
            onClick={onLearnMore}
          >
            <BookOpen className="h-3 w-3" />
            Learn More
          </Button>
          
          <Button 
            variant="ghost" 
            size="sm" 
            className="gap-2 text-muted-foreground"
            onClick={() => {
              // Open GEO best practices guide
              window.open("https://docs.useadmesh.com/geo-optimization", "_blank");
            }}
          >
            <ExternalLink className="h-3 w-3" />
            GEO Guide
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
