"use client";

import { useState, useEffect, useCallback } from "react";
import { useAuth } from "@/hooks/use-auth";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import {
  Search,
  AlertCircle,
  Target,
  BarChart3,
  RefreshCw
} from "lucide-react";
import { GEOScoreCard } from "@/components/geo-check/GEOScoreCard";
import { GEORecommendationCard } from "@/components/geo-check/GEORecommendationCard";
import { GEOInfoCard } from "@/components/geo-check/GEOInfoCard";

interface GEOAnalysis {
  overallScore: number;
  aiDiscoverability: {
    score: number;
    mentions: number;
    sentiment: "positive" | "neutral" | "negative";
    topQueries: string[];
  };
  contentOptimization: {
    score: number;
    structureScore: number;
    factualClaimsScore: number;
    aiReadabilityScore: number;
  };
  competitiveAnalysis: {
    shareOfVoice: number;
    competitorMentions: { name: string; mentions: number }[];
  };
  recommendations: {
    priority: "high" | "medium" | "low";
    category: string;
    title: string;
    description: string;
    impact: string;
  }[];
}

export default function GEOCheckPage() {
  const { user } = useAuth();
  const [analysis, setAnalysis] = useState<GEOAnalysis | null>(null);
  const [loading, setLoading] = useState(false);
  const [brandData, setBrandData] = useState<{
    website?: string;
    company_name?: string;
    industry?: string;
  } | null>(null);

  const fetchBrandData = useCallback(async () => {
    if (!user) return;

    try {
      const token = await user.getIdToken();
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/brands/profile`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setBrandData(data);
      }
    } catch (error) {
      console.error("Error fetching brand data:", error);
    }
  }, [user]);

  useEffect(() => {
    fetchBrandData();
  }, [fetchBrandData]);

  const runGEOAnalysis = async () => {
    if (!brandData?.website) {
      alert("Please add your website in the brand profile first.");
      return;
    }

    setLoading(true);
    try {
      const token = await user?.getIdToken();
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/brands/geo-check`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          website: brandData.website,
          company_name: brandData.company_name,
          industry: brandData.industry,
        }),
      });

      if (response.ok) {
        const data = await response.json();
        setAnalysis(data);
      } else {
        throw new Error("Failed to run GEO analysis");
      }
    } catch (error) {
      console.error("Error running GEO analysis:", error);
      alert("Failed to run GEO analysis. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return "text-green-600";
    if (score >= 60) return "text-yellow-600";
    return "text-red-600";
  };

  const getScoreBadgeVariant = (score: number) => {
    if (score >= 80) return "default";
    if (score >= 60) return "secondary";
    return "destructive";
  };



  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">GEO Check</h2>
          <p className="text-muted-foreground text-sm">
            Analyze your brand&apos;s visibility in AI-powered search engines
          </p>
        </div>
        <Button 
          onClick={runGEOAnalysis} 
          disabled={loading || !brandData?.website}
          className="gap-2"
        >
          {loading ? (
            <RefreshCw className="h-4 w-4 animate-spin" />
          ) : (
            <Search className="h-4 w-4" />
          )}
          {loading ? "Analyzing..." : "Run GEO Analysis"}
        </Button>
      </div>

      {/* What is GEO Info Card */}
      <GEOInfoCard />

      {!analysis && !loading && (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <Search className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium mb-2">Ready to analyze your GEO performance?</h3>
            <p className="text-muted-foreground text-center mb-6 max-w-md">
              Get insights on how your brand appears in AI-generated responses and discover opportunities 
              to improve your visibility in the age of generative AI.
            </p>
            {!brandData?.website && (
              <div className="flex items-center gap-2 text-amber-600 mb-4">
                <AlertCircle className="h-4 w-4" />
                <span className="text-sm">Please add your website in brand settings first</span>
              </div>
            )}
            <Button onClick={runGEOAnalysis} disabled={!brandData?.website} className="gap-2">
              <Search className="h-4 w-4" />
              Start GEO Analysis
            </Button>
          </CardContent>
        </Card>
      )}

      {analysis && (
        <Tabs defaultValue="overview" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="discoverability">AI Discoverability</TabsTrigger>
            <TabsTrigger value="content">Content Analysis</TabsTrigger>
            <TabsTrigger value="recommendations">Recommendations</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            {/* Overall Score */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  Overall GEO Score
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center gap-4">
                  <div className="text-4xl font-bold">
                    <span className={getScoreColor(analysis.overallScore)}>
                      {analysis.overallScore}
                    </span>
                    <span className="text-muted-foreground text-lg">/100</span>
                  </div>
                  <div className="flex-1">
                    <Progress value={analysis.overallScore} className="h-3" />
                  </div>
                  <Badge variant={getScoreBadgeVariant(analysis.overallScore)}>
                    {analysis.overallScore >= 80 ? "Excellent" : 
                     analysis.overallScore >= 60 ? "Good" : "Needs Improvement"}
                  </Badge>
                </div>
              </CardContent>
            </Card>

            {/* Key Metrics Grid */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <GEOScoreCard
                title="AI Discoverability"
                score={analysis.aiDiscoverability.score}
                description={`${analysis.aiDiscoverability.mentions} mentions found`}
              />
              <GEOScoreCard
                title="Content Optimization"
                score={analysis.contentOptimization.score}
                description="AI-friendly structure"
              />
              <GEOScoreCard
                title="Share of Voice"
                score={analysis.competitiveAnalysis.shareOfVoice}
                maxScore={100}
                description="vs competitors"
              />
            </div>
          </TabsContent>

          <TabsContent value="discoverability" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Target className="h-5 w-5" />
                    AI Mention Analysis
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Total Mentions</span>
                    <Badge variant="outline">{analysis.aiDiscoverability.mentions}</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Sentiment</span>
                    <Badge variant={
                      analysis.aiDiscoverability.sentiment === "positive" ? "default" :
                      analysis.aiDiscoverability.sentiment === "neutral" ? "secondary" : "destructive"
                    }>
                      {analysis.aiDiscoverability.sentiment}
                    </Badge>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Top Queries</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {analysis.aiDiscoverability.topQueries.map((query, index) => (
                      <div key={index} className="text-sm p-2 bg-muted rounded">
                        &quot;{query}&quot;
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="content" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <GEOScoreCard
                title="Structure Score"
                score={analysis.contentOptimization.structureScore}
                description="Heading hierarchy and organization"
              />
              <GEOScoreCard
                title="Factual Claims"
                score={analysis.contentOptimization.factualClaimsScore}
                description="Evidence-based content"
              />
              <GEOScoreCard
                title="AI Readability"
                score={analysis.contentOptimization.aiReadabilityScore}
                description="Optimized for AI consumption"
              />
            </div>
          </TabsContent>

          <TabsContent value="recommendations" className="space-y-4">
            {analysis.recommendations.map((rec, index) => (
              <GEORecommendationCard
                key={index}
                recommendation={rec}
              />
            ))}
          </TabsContent>
        </Tabs>
      )}
    </div>
  );
}
